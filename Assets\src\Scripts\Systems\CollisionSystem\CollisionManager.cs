using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

public class CollisionManager : MonoBehaviour
{
        [Title("Collision Settings")]
        [SerializeField]
        [Range(0.5f, 4f)]
        private float spatialGridCellSize = 2f;
        
        [SerializeField]
        [Required]
        private TilemapChunkManager chunkManager;
        
        // Number of neighbouring chunk rings (in X and Y direction) that should be considered active in addition to the current player chunk.
        // A value of 0 behaves like the previous implementation (only the current chunk).
        [SerializeField, MinValue(0)]
        private int neighbourChunkRange = 1;
        
        [Title("Debug")]
        [ShowInInspector, ReadOnly]
        private int activeCollidersCount;
        
        [ShowInInspector, ReadOnly]
        private ChunkCoordinate currentChunk;
        
        // Debug event toggles
        [Title("Debug Events")]
        [SerializeField]
        private bool logCollisionEvents = false;
        
        [SerializeField]
        private bool logTriggerEvents = false;
        
        // Public accessors for other classes
        public bool LogCollisionEvents => logCollisionEvents;
        public bool LogTriggerEvents => logTriggerEvents;
        
        public static CollisionManager Instance { get; private set; }
        
        private SpatialHashGrid _spatialGrid;
        private readonly List<ICollidable> _activeCollidables = new List<ICollidable>();
        private readonly Dictionary<ICollidable, HashSet<ICollidable>> _currentCollisions = new Dictionary<ICollidable, HashSet<ICollidable>>();
        private readonly Queue<CollisionInfo> _collisionInfoPool = new Queue<CollisionInfo>();

        // Pooled collections to reduce GC pressure
        private readonly List<ICollidable> _reusableCollidablesList = new List<ICollidable>();
        private readonly HashSet<ICollidable> _reusableCollidablesHashSet = new HashSet<ICollidable>();
        private readonly List<ICollidable> _reusableRadiusResults = new List<ICollidable>();
        private readonly HashSet<ICollidable> _reusableRadiusChecked = new HashSet<ICollidable>();
        
        private ChunkCoordinate _lastPlayerChunk = new ChunkCoordinate(int.MinValue, int.MinValue);
        private Vector2 _currentChunkMin;
        private Vector2 _currentChunkMax;
        
        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
                return;
            }
            Instance = this;
            
            _spatialGrid = new SpatialHashGrid(spatialGridCellSize);
            
            // Pre-pool collision info objects
            for (int i = 0; i < 100; i++)
            {
                _collisionInfoPool.Enqueue(new CollisionInfo());
            }
        }
        
        public void RegisterCollidable(ICollidable collidable)
        {
            if (!_activeCollidables.Contains(collidable))
            {
                _activeCollidables.Add(collidable);
                _currentCollisions[collidable] = new HashSet<ICollidable>();
                
                // Only add to spatial grid if within current chunk
                if (IsInCurrentChunk(collidable.Position))
                {
                    _spatialGrid.Add(collidable);
                }
            }
        }
        
        public void UnregisterCollidable(ICollidable collidable)
        {
            _activeCollidables.Remove(collidable);
            _spatialGrid.Remove(collidable);
            
            // Clean up any remaining collisions
            if (_currentCollisions.TryGetValue(collidable, out var collisions))
            {
                foreach (var other in collisions)
                {
                    var info = GetCollisionInfo();
                    info.Other = other;
                    if (collidable.IsTrigger || other.IsTrigger)
                    {
                        collidable.OnSpatialTriggerExit(info);
                    }
                    else
                    {
                        collidable.OnSpatialCollisionExit(info);
                    }
                    ReturnCollisionInfo(info);
                }
                _currentCollisions.Remove(collidable);
            }
        }
        
        public void UpdateCollidablePosition(ICollidable collidable)
        {
            if (IsInCurrentChunk(collidable.Position))
            {
                _spatialGrid.Update(collidable);
            }
            else
            {
                _spatialGrid.Remove(collidable);
            }
        }
        
        private void FixedUpdate()
        {
            UpdateCurrentChunk();
            ProcessCollisions();
            activeCollidersCount = _activeCollidables.Count;
        }
        
        private void UpdateCurrentChunk()
        {
            if (chunkManager == null) return;
            
            var playerChunk = chunkManager.GetCurrentPlayerChunk();
            if (playerChunk != _lastPlayerChunk)
            {
                _lastPlayerChunk = playerChunk;
                currentChunk = playerChunk;
                
                // Calculate extended bounds that encompass the neighbouring chunks defined by neighbourChunkRange
                var chunkWidth = chunkManager.GetChunkWidth();
                var chunkHeight = chunkManager.GetChunkHeight();

                // Min corner: shift left/down by neighbourChunkRange chunks
                _currentChunkMin = new Vector2(
                    (playerChunk.x - neighbourChunkRange) * chunkWidth,
                    (playerChunk.y - neighbourChunkRange) * chunkHeight);

                // Max corner: extend right/up by (1 + neighbourChunkRange) chunks because chunk coordinates are inclusive on min but exclusive on max
                _currentChunkMax = new Vector2(
                    (playerChunk.x + 1 + neighbourChunkRange) * chunkWidth,
                    (playerChunk.y + 1 + neighbourChunkRange) * chunkHeight);
                
                // Clear spatial grid and rebuild for new chunk
                _spatialGrid.Clear();
                
                // Re-add only collidables in current chunk
                foreach (var collidable in _activeCollidables)
                {
                    if (IsInCurrentChunk(collidable.Position))
                    {
                        _spatialGrid.Add(collidable);
                    }
                }
                
                // Clear all existing collisions
                foreach (var kvp in _currentCollisions)
                {
                    kvp.Value.Clear();
                }
            }
        }
        
        private bool IsInCurrentChunk(Vector2 position)
        {
            return position.x >= _currentChunkMin.x && position.x < _currentChunkMax.x &&
                   position.y >= _currentChunkMin.y && position.y < _currentChunkMax.y;
        }
        
        private void ProcessCollisions()
        {
            // Use pooled list instead of creating new one
            _reusableCollidablesList.Clear();
            _reusableCollidablesList.AddRange(_activeCollidables);

            // Process each active collidable
            foreach (var collidable in _reusableCollidablesList)
            {
                if (!_currentCollisions.TryGetValue(collidable, out var previousCollisions))
                    continue;

                if (!collidable.IsActive || !IsInCurrentChunk(collidable.Position))
                    continue;
                
                var nearby = _spatialGrid.GetNearby(collidable);

                // Use pooled HashSet instead of creating new one
                _reusableCollidablesHashSet.Clear();
                
                foreach (var other in nearby)
                {
                    if (!CanCollide(collidable, other))
                        continue;
                    
                    if (CheckCollision(collidable, other, out var info))
                    {
                        _reusableCollidablesHashSet.Add(other);
                        
                        bool wasColliding = previousCollisions.Contains(other);
                        
                        if (collidable.IsTrigger || other.IsTrigger)
                        {
                            if (!wasColliding)
                            {
                                collidable.OnSpatialTriggerEnter(info);
                                
                                // Reverse collision info for other object
                                var reverseInfo = GetCollisionInfo();
                                reverseInfo.Other = collidable;
                                reverseInfo.ContactPoint = info.ContactPoint;
                                reverseInfo.Normal = -info.Normal;
                                reverseInfo.Penetration = info.Penetration;
                                other.OnSpatialTriggerEnter(reverseInfo);
                                ReturnCollisionInfo(reverseInfo);
                            }
                            else
                            {
                                collidable.OnSpatialTriggerStay(info);
                                
                                var reverseInfo = GetCollisionInfo();
                                reverseInfo.Other = collidable;
                                reverseInfo.ContactPoint = info.ContactPoint;
                                reverseInfo.Normal = -info.Normal;
                                reverseInfo.Penetration = info.Penetration;
                                other.OnSpatialTriggerStay(reverseInfo);
                                ReturnCollisionInfo(reverseInfo);
                            }
                        }
                        else
                        {
                            if (!wasColliding)
                            {
                                collidable.OnSpatialCollisionEnter(info);
                                
                                var reverseInfo = GetCollisionInfo();
                                reverseInfo.Other = collidable;
                                reverseInfo.ContactPoint = info.ContactPoint;
                                reverseInfo.Normal = -info.Normal;
                                reverseInfo.Penetration = info.Penetration;
                                other.OnSpatialCollisionEnter(reverseInfo);
                                ReturnCollisionInfo(reverseInfo);
                            }
                            else
                            {
                                collidable.OnSpatialCollisionStay(info);
                                
                                var reverseInfo = GetCollisionInfo();
                                reverseInfo.Other = collidable;
                                reverseInfo.ContactPoint = info.ContactPoint;
                                reverseInfo.Normal = -info.Normal;
                                reverseInfo.Penetration = info.Penetration;
                                other.OnSpatialCollisionStay(reverseInfo);
                                ReturnCollisionInfo(reverseInfo);
                            }
                        }
                        
                        ReturnCollisionInfo(info);
                    }
                }
                
                // Check for exits
                foreach (var previousCollision in previousCollisions)
                    {
                        if (!_reusableCollidablesHashSet.Contains(previousCollision))
                        {
                            var info = GetCollisionInfo();
                            info.Other = previousCollision;
                        
                        if (collidable.IsTrigger || previousCollision.IsTrigger)
                        {
                            collidable.OnSpatialTriggerExit(info);
                            
                            var reverseInfo = GetCollisionInfo();
                            reverseInfo.Other = collidable;
                            previousCollision.OnSpatialTriggerExit(reverseInfo);
                            ReturnCollisionInfo(reverseInfo);
                        }
                        else
                        {
                            collidable.OnSpatialCollisionExit(info);
                            
                            var reverseInfo = GetCollisionInfo();
                            reverseInfo.Other = collidable;
                            previousCollision.OnSpatialCollisionExit(reverseInfo);
                            ReturnCollisionInfo(reverseInfo);
                        }
                        
                        ReturnCollisionInfo(info);
                    }
                }

                // Create a new HashSet for storage (we can't store the reusable one directly)
                _currentCollisions[collidable] = new HashSet<ICollidable>(_reusableCollidablesHashSet);
            }
        }
        
        private bool CanCollide(ICollidable a, ICollidable b)
        {
            return CollisionLayerMatrix.CanLayersCollide(a.Layer, b.Layer);
        }
        
        private bool CheckCollision(ICollidable a, ICollidable b, out CollisionInfo info)
        {
            info = GetCollisionInfo();
            info.Other = b;
            
            // Circle vs Circle
            if (a.Shape == ColliderShape.Circle && b.Shape == ColliderShape.Circle)
            {
                return CircleVsCircle(a, b, info);
            }
            // Box vs Box
            else if (a.Shape == ColliderShape.Box && b.Shape == ColliderShape.Box)
            {
                return BoxVsBox(a, b, info);
            }
            // Circle vs Box
            else if (a.Shape == ColliderShape.Circle && b.Shape == ColliderShape.Box)
            {
                return CircleVsBox(a, b, info);
            }
            // Box vs Circle
            else
            {
                bool result = CircleVsBox(b, a, info);
                if (result)
                {
                    info.Normal = -info.Normal;
                    info.Other = b;
                }
                return result;
            }
        }
        
        private bool CircleVsCircle(ICollidable a, ICollidable b, CollisionInfo info)
        {
            Vector2 diff = b.Position - a.Position;
            float distSq = diff.sqrMagnitude;
            float radiusSum = a.Radius + b.Radius;
            
            if (distSq < radiusSum * radiusSum)
            {
                float dist = Mathf.Sqrt(distSq);
                info.Normal = dist > 0 ? diff / dist : Vector2.right;
                info.Penetration = radiusSum - dist;
                info.ContactPoint = a.Position + info.Normal * a.Radius;
                return true;
            }
            
            return false;
        }
        
        private bool BoxVsBox(ICollidable a, ICollidable b, CollisionInfo info)
        {
            Vector2 aMin = a.Position - a.Size * 0.5f;
            Vector2 aMax = a.Position + a.Size * 0.5f;
            Vector2 bMin = b.Position - b.Size * 0.5f;
            Vector2 bMax = b.Position + b.Size * 0.5f;
            
            if (aMin.x < bMax.x && aMax.x > bMin.x &&
                aMin.y < bMax.y && aMax.y > bMin.y)
            {
                // Calculate overlap on each axis
                float overlapX = Mathf.Min(aMax.x - bMin.x, bMax.x - aMin.x);
                float overlapY = Mathf.Min(aMax.y - bMin.y, bMax.y - aMin.y);
                
                // Find the axis of least penetration
                if (overlapX < overlapY)
                {
                    info.Penetration = overlapX;
                    info.Normal = a.Position.x < b.Position.x ? Vector2.left : Vector2.right;
                    info.ContactPoint = new Vector2(
                        a.Position.x < b.Position.x ? aMax.x : aMin.x,
                        Mathf.Clamp(b.Position.y, aMin.y, aMax.y)
                    );
                }
                else
                {
                    info.Penetration = overlapY;
                    info.Normal = a.Position.y < b.Position.y ? Vector2.down : Vector2.up;
                    info.ContactPoint = new Vector2(
                        Mathf.Clamp(b.Position.x, aMin.x, aMax.x),
                        a.Position.y < b.Position.y ? aMax.y : aMin.y
                    );
                }
                
                return true;
            }
            
            return false;
        }
        
        private bool CircleVsBox(ICollidable circle, ICollidable box, CollisionInfo info)
        {
            Vector2 boxMin = box.Position - box.Size * 0.5f;
            Vector2 boxMax = box.Position + box.Size * 0.5f;
            
            // Find closest point on box to circle center
            Vector2 closest = new Vector2(
                Mathf.Clamp(circle.Position.x, boxMin.x, boxMax.x),
                Mathf.Clamp(circle.Position.y, boxMin.y, boxMax.y)
            );
            
            Vector2 diff = circle.Position - closest;
            float distSq = diff.sqrMagnitude;
            
            if (distSq < circle.Radius * circle.Radius)
            {
                float dist = Mathf.Sqrt(distSq);
                info.Normal = dist > 0 ? diff / dist : Vector2.up;
                info.Penetration = circle.Radius - dist;
                info.ContactPoint = closest;
                return true;
            }
            
            return false;
        }
        
        private CollisionInfo GetCollisionInfo()
        {
            if (_collisionInfoPool.Count > 0)
                return _collisionInfoPool.Dequeue();
            return new CollisionInfo();
        }
        
        private void ReturnCollisionInfo(CollisionInfo info)
        {
            info.Reset();
            _collisionInfoPool.Enqueue(info);
        }
        
        /// <summary>
        /// Gets all collidables within a radius from a position, filtered by layer mask.
        /// </summary>
        public List<ICollidable> GetCollidersInRadius(Vector2 position, float radius, CollisionLayers layerMask = CollisionLayers.All)
        {
            // Use pooled collections instead of creating new ones
            _reusableRadiusResults.Clear();

            if (_spatialGrid == null) return _reusableRadiusResults;

            // Get all potentially overlapping cells
            var cells = _spatialGrid.GetCellsInRadius(position, radius);
            _reusableRadiusChecked.Clear();
            
            foreach (var cell in cells)
            {
                var collidables = _spatialGrid.GetCollidablesInCell(cell.x, cell.y);
                if (collidables != null)
                {
                    foreach (var collidable in collidables)
                    {
                        // Skip if already checked or doesn't match layer mask
                        if (_reusableRadiusChecked.Contains(collidable) || !layerMask.HasFlag(collidable.Layer))
                            continue;

                        _reusableRadiusChecked.Add(collidable);
                        
                        // Check actual distance based on shape
                        bool inRadius = false;
                        
                        if (collidable.Shape == ColliderShape.Circle)
                        {
                            // Circle to point distance
                            float distance = Vector2.Distance(position, collidable.Position);
                            inRadius = distance <= radius + collidable.Radius;
                        }
                        else if (collidable.Shape == ColliderShape.Box)
                        {
                            // Box to point distance
                            Vector2 boxMin = collidable.Position - collidable.Size * 0.5f;
                            Vector2 boxMax = collidable.Position + collidable.Size * 0.5f;
                            
                            Vector2 closest = new Vector2(
                                Mathf.Clamp(position.x, boxMin.x, boxMax.x),
                                Mathf.Clamp(position.y, boxMin.y, boxMax.y)
                            );
                            
                            float distance = Vector2.Distance(position, closest);
                            inRadius = distance <= radius;
                        }
                        
                        if (inRadius)
                        {
                            _reusableRadiusResults.Add(collidable);
                        }
                    }
                }
            }
            
            return results;
        }

#if UNITY_EDITOR
        private void OnDrawGizmos()
        {
            if (!Application.isPlaying) return;

            // Draw the bounds of the current chunk being processed by the CollisionManager
            Gizmos.color = Color.cyan;
            Vector2 size = _currentChunkMax - _currentChunkMin;
            Vector2 center = _currentChunkMin + size / 2f;

            if (size != Vector2.zero)
            {
                Gizmos.DrawWireCube(center, size);
            }
        }
#endif
    }